#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <getopt.h>
#include <pthread.h>
#include <sched.h>
#include <alsa/asoundlib.h>

// 音频参数定义
#define SAMPLE_RATE     48000
#define DEFAULT_CAPTURE_CHANNELS 32
#define DEFAULT_PLAYBACK_CHANNELS 2
#define FORMAT          SND_PCM_FORMAT_S32_LE
#define SAMPLE_SIZE     4  // S32_LE每样本4字节

// 全局变量
static int running = 1;
static snd_pcm_t *capture_handle = NULL;
static snd_pcm_t *playback_handle = NULL;
static int ch1 = 0, ch2 = 1;  // 默认选择通道0和1
static int frame_time = 4;    // 默认4ms帧长
static int debug_mode = 0;    // 调试模式
static char capture_device[64] = "hw:0,0";  // 默认采集设备
static char playback_device[64] = "hw:2,0"; // 默认播放设备
static int capture_channels = DEFAULT_CAPTURE_CHANNELS;  // 采集通道数
static int playback_channels = DEFAULT_PLAYBACK_CHANNELS; // 播放通道数
static int use_mmap = 0;      // 是否使用mmap模式

// 信号处理函数
void signal_handler(int sig) {
    printf("\n接收到信号 %d, 正在退出...\n", sig);
    running = 0;
}

// 设置进程优先级
int set_high_priority() {
    struct sched_param param;
    param.sched_priority = 90;  // 设置为高优先级
    
    if (sched_setscheduler(0, SCHED_FIFO, &param) != 0) {
        perror("设置实时优先级失败");
        return -1;
    }
    
    printf("进程优先级已设置为实时模式，优先级: %d\n", param.sched_priority);
    return 0;
}

// 初始化ALSA采集设备
int init_capture_device() {
    int err;
    snd_pcm_hw_params_t *hw_params;

    // 打开PCM设备 - 使用阻塞模式确保稳定性
    if ((err = snd_pcm_open(&capture_handle, capture_device, SND_PCM_STREAM_CAPTURE, 0)) < 0) {
        fprintf(stderr, "无法打开采集设备 %s: %s\n", capture_device, snd_strerror(err));
        return -1;
    }
    
    // 分配硬件参数结构体
    if ((err = snd_pcm_hw_params_malloc(&hw_params)) < 0) {
        fprintf(stderr, "无法分配硬件参数结构体: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 初始化硬件参数
    if ((err = snd_pcm_hw_params_any(capture_handle, hw_params)) < 0) {
        fprintf(stderr, "无法初始化硬件参数: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置访问模式 - 根据是否使用mmap选择不同模式
    snd_pcm_access_t access_type = use_mmap ? SND_PCM_ACCESS_MMAP_INTERLEAVED : SND_PCM_ACCESS_RW_INTERLEAVED;
    if ((err = snd_pcm_hw_params_set_access(capture_handle, hw_params, access_type)) < 0) {
        fprintf(stderr, "无法设置访问模式: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置采样格式
    if ((err = snd_pcm_hw_params_set_format(capture_handle, hw_params, FORMAT)) < 0) {
        fprintf(stderr, "无法设置采样格式: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置采样率
    unsigned int rate = SAMPLE_RATE;
    if ((err = snd_pcm_hw_params_set_rate_near(capture_handle, hw_params, &rate, 0)) < 0) {
        fprintf(stderr, "无法设置采样率: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置通道数
    if ((err = snd_pcm_hw_params_set_channels(capture_handle, hw_params, capture_channels)) < 0) {
        fprintf(stderr, "无法设置通道数: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置周期大小（帧数）
    snd_pcm_uframes_t frames = (SAMPLE_RATE * frame_time) / 1000;
    if ((err = snd_pcm_hw_params_set_period_size_near(capture_handle, hw_params, &frames, 0)) < 0) {
        fprintf(stderr, "无法设置周期大小: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置缓冲区大小 - 低延迟模式使用更小的缓冲区
    snd_pcm_uframes_t buffer_size = frames * 2;
    if ((err = snd_pcm_hw_params_set_buffer_size_near(capture_handle, hw_params, &buffer_size)) < 0) {
        fprintf(stderr, "无法设置缓冲区大小: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 应用硬件参数
    if ((err = snd_pcm_hw_params(capture_handle, hw_params)) < 0) {
        fprintf(stderr, "无法应用硬件参数: %s\n", snd_strerror(err));
        return -1;
    }
    
    snd_pcm_hw_params_free(hw_params);

    // 低延迟模式下设置软件参数
    if (0) {
        snd_pcm_sw_params_t *sw_params;
        snd_pcm_sw_params_malloc(&sw_params);
        snd_pcm_sw_params_current(capture_handle, sw_params);

        // 设置启动阈值为1个周期
        snd_pcm_sw_params_set_start_threshold(capture_handle, sw_params, frames);
        // 设置可用最小值为1个周期
        snd_pcm_sw_params_set_avail_min(capture_handle, sw_params, frames);

        snd_pcm_sw_params(capture_handle, sw_params);
        snd_pcm_sw_params_free(sw_params);
    }

    // 准备PCM设备
    if ((err = snd_pcm_prepare(capture_handle)) < 0) {
        fprintf(stderr, "无法准备PCM设备: %s\n", snd_strerror(err));
        return -1;
    }
    
    printf("采集设备初始化成功: %s, %d通道, %dHz, %dms帧长\n",
           capture_device, capture_channels, SAMPLE_RATE, frame_time);
    
    return 0;
}

// 初始化ALSA播放设备
int init_playback_device() {
    int err;
    snd_pcm_hw_params_t *hw_params;

    // 打开PCM设备 - 使用阻塞模式确保稳定性
    if ((err = snd_pcm_open(&playback_handle, playback_device, SND_PCM_STREAM_PLAYBACK, 0)) < 0) {
        fprintf(stderr, "无法打开播放设备 %s: %s\n", playback_device, snd_strerror(err));
        return -1;
    }
    
    // 分配硬件参数结构体
    if ((err = snd_pcm_hw_params_malloc(&hw_params)) < 0) {
        fprintf(stderr, "无法分配硬件参数结构体: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 初始化硬件参数
    if ((err = snd_pcm_hw_params_any(playback_handle, hw_params)) < 0) {
        fprintf(stderr, "无法初始化硬件参数: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置访问模式 - 根据是否使用mmap选择不同模式
    snd_pcm_access_t access_type = use_mmap ? SND_PCM_ACCESS_MMAP_INTERLEAVED : SND_PCM_ACCESS_RW_INTERLEAVED;
    if ((err = snd_pcm_hw_params_set_access(playback_handle, hw_params, access_type)) < 0) {
        fprintf(stderr, "无法设置访问模式: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置采样格式
    if ((err = snd_pcm_hw_params_set_format(playback_handle, hw_params, FORMAT)) < 0) {
        fprintf(stderr, "无法设置采样格式: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置采样率
    unsigned int rate = SAMPLE_RATE;
    if ((err = snd_pcm_hw_params_set_rate_near(playback_handle, hw_params, &rate, 0)) < 0) {
        fprintf(stderr, "无法设置采样率: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置通道数
    if ((err = snd_pcm_hw_params_set_channels(playback_handle, hw_params, playback_channels)) < 0) {
        fprintf(stderr, "无法设置通道数: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置周期大小（帧数）
    snd_pcm_uframes_t frames = (SAMPLE_RATE * frame_time) / 1000;
    if ((err = snd_pcm_hw_params_set_period_size_near(playback_handle, hw_params, &frames, 0)) < 0) {
        fprintf(stderr, "无法设置周期大小: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置缓冲区大小 - 低延迟模式使用更小的缓冲区
    snd_pcm_uframes_t buffer_size = frames * 2;
    if ((err = snd_pcm_hw_params_set_buffer_size_near(playback_handle, hw_params, &buffer_size)) < 0) {
        fprintf(stderr, "无法设置缓冲区大小: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 应用硬件参数
    if ((err = snd_pcm_hw_params(playback_handle, hw_params)) < 0) {
        fprintf(stderr, "无法应用硬件参数: %s\n", snd_strerror(err));
        return -1;
    }
    
    snd_pcm_hw_params_free(hw_params);

    // 低延迟模式下设置软件参数
    if (0) {
        snd_pcm_sw_params_t *sw_params;
        snd_pcm_sw_params_malloc(&sw_params);
        snd_pcm_sw_params_current(playback_handle, sw_params);

        // 设置启动阈值为1个周期
        snd_pcm_sw_params_set_start_threshold(playback_handle, sw_params, frames);
        // 设置可用最小值为1个周期
        snd_pcm_sw_params_set_avail_min(playback_handle, sw_params, frames);

        snd_pcm_sw_params(playback_handle, sw_params);
        snd_pcm_sw_params_free(sw_params);
    }

    // 准备PCM设备
    if ((err = snd_pcm_prepare(playback_handle)) < 0) {
        fprintf(stderr, "无法准备PCM设备: %s\n", snd_strerror(err));
        return -1;
    }
    
    printf("播放设备初始化成功: %s, %d通道, %dHz\n",
           playback_device, playback_channels, SAMPLE_RATE);
    
    return 0;
}

// 恢复播放设备
int recover_playback() {
    int err;
    printf("播放设备缓冲区下溢，恢复中...\n");
    
    if ((err = snd_pcm_prepare(playback_handle)) < 0) {
        fprintf(stderr, "无法准备播放设备: %s\n", snd_strerror(err));
        return err;
    }
    
    // 预填充一些静音数据到播放缓冲区
    snd_pcm_uframes_t frames_to_process = (SAMPLE_RATE * frame_time) / 1000;
    int32_t *silence_buffer = calloc(frames_to_process * playback_channels, SAMPLE_SIZE);
    if (silence_buffer) {
        // 写入2个周期的静音数据
        for (int i = 0; i < 2; i++) {
            snd_pcm_writei(playback_handle, silence_buffer, frames_to_process);
        }
        free(silence_buffer);
    }
    
    return 0;
}

// MMAP方式读取音频数据
int mmap_read_audio(snd_pcm_t *handle, int32_t *buffer, snd_pcm_uframes_t frames, int channels) {
    const snd_pcm_channel_area_t *areas;
    snd_pcm_uframes_t offset, frames_to_read;
    int err;

    frames_to_read = frames;

    while (frames_to_read > 0) {
        err = snd_pcm_mmap_begin(handle, &areas, &offset, &frames_to_read);
        if (err < 0) {
            if (err == -EPIPE) {
                printf("采集设备缓冲区溢出，恢复中...\n");
                snd_pcm_prepare(handle);
                return err;
            }
            fprintf(stderr, "mmap_begin错误: %s\n", snd_strerror(err));
            return err;
        }

        // 复制数据从mmap区域到我们的缓冲区
        int32_t *src = (int32_t *)((char *)areas[0].addr + (areas[0].first + offset * areas[0].step) / 8);
        int32_t *dst = buffer;

        for (snd_pcm_uframes_t i = 0; i < frames_to_read; i++) {
            for (int ch = 0; ch < channels; ch++) {
                dst[i * channels + ch] = src[i * channels + ch];
            }
        }

        err = snd_pcm_mmap_commit(handle, offset, frames_to_read);
        if (err < 0 || (snd_pcm_uframes_t)err != frames_to_read) {
            fprintf(stderr, "mmap_commit错误: %s\n", snd_strerror(err));
            return err < 0 ? err : -EIO;
        }

        buffer += frames_to_read * channels;
        frames_to_read = frames - frames_to_read;
    }

    return frames;
}

// MMAP方式写入音频数据
int mmap_write_audio(snd_pcm_t *handle, int32_t *buffer, snd_pcm_uframes_t frames, int channels) {
    const snd_pcm_channel_area_t *areas;
    snd_pcm_uframes_t offset, frames_to_write;
    int err;

    frames_to_write = frames;

    while (frames_to_write > 0) {
        err = snd_pcm_mmap_begin(handle, &areas, &offset, &frames_to_write);
        if (err < 0) {
            if (err == -EPIPE) {
                printf("播放设备缓冲区下溢，恢复中...\n");
                if (recover_playback() < 0) {
                    return err;
                }
                continue;
            }
            fprintf(stderr, "mmap_begin错误: %s\n", snd_strerror(err));
            return err;
        }

        // 复制数据从我们的缓冲区到mmap区域
        int32_t *dst = (int32_t *)((char *)areas[0].addr + (areas[0].first + offset * areas[0].step) / 8);
        int32_t *src = buffer;

        for (snd_pcm_uframes_t i = 0; i < frames_to_write; i++) {
            for (int ch = 0; ch < channels; ch++) {
                dst[i * channels + ch] = src[i * channels + ch];
            }
        }

        err = snd_pcm_mmap_commit(handle, offset, frames_to_write);
        if (err < 0 || (snd_pcm_uframes_t)err != frames_to_write) {
            fprintf(stderr, "mmap_commit错误: %s\n", snd_strerror(err));
            return err < 0 ? err : -EIO;
        }

        buffer += frames_to_write * channels;
        frames_to_write = frames - frames_to_write;
    }

    return frames;
}

// 音频处理主循环
void audio_loop() {
    int err;
    snd_pcm_uframes_t frames_to_process = (SAMPLE_RATE * frame_time) / 1000;
    static int underrun_count = 0;

    // 分配缓冲区
    int32_t *capture_buffer = malloc(frames_to_process * capture_channels * SAMPLE_SIZE);
    int32_t *playback_buffer = malloc(frames_to_process * playback_channels * SAMPLE_SIZE);
    
    if (!capture_buffer || !playback_buffer) {
        fprintf(stderr, "无法分配音频缓冲区\n");
        return;
    }
    
    // 预填充播放缓冲区，避免开始时的下溢 - 低延迟模式减少预填充
    memset(playback_buffer, 0, frames_to_process * playback_channels * SAMPLE_SIZE);
    int prefill_periods = 1;
    for (int i = 0; i < prefill_periods; i++) {
        snd_pcm_writei(playback_handle, playback_buffer, frames_to_process);
    }
    
    // 启动采集设备
    if ((err = snd_pcm_start(capture_handle)) < 0) {
        fprintf(stderr, "无法启动采集设备: %s\n", snd_strerror(err));
        free(capture_buffer);
        free(playback_buffer);
        return;
    }

    printf("开始音频处理循环，选择通道: %d, %d\n", ch1, ch2);
    printf("按 Ctrl+C 停止程序\n");
    
    while (running) {
        // 从采集设备读取数据 - 根据模式选择读取方式
        if (use_mmap) {
            err = mmap_read_audio(capture_handle, capture_buffer, frames_to_process, capture_channels);
        } else {
            err = snd_pcm_readi(capture_handle, capture_buffer, frames_to_process);
        }

        if (err == -EAGAIN) {
            usleep(100); // 短暂等待
            continue;
        } else if (err < 0) {
            if (err == -EPIPE) {
                printf("采集设备缓冲区溢出，恢复中...\n");
                snd_pcm_prepare(capture_handle);
            } else {
                fprintf(stderr, "读取采集数据错误: %s\n", snd_strerror(err));
                break;
            }
            continue;
        }

        // 检查是否读取到预期的帧数
        if (err != (int)frames_to_process) {
            if (debug_mode) {
                printf("警告: 只读取到 %d 帧，预期 %lu 帧\n", err, frames_to_process);
            }
            continue;
        }

        // 提取选定的通道数据
        for (snd_pcm_uframes_t i = 0; i < frames_to_process; i++) {
            // 根据播放通道数复制数据
            for (int j = 0; j < playback_channels && j < 2; j++) {
                int src_ch = (j == 0) ? ch1 : ch2;
                if (src_ch < capture_channels) {
                    playback_buffer[i * playback_channels + j] =
                        capture_buffer[i * capture_channels + src_ch];
                } else {
                    playback_buffer[i * playback_channels + j] = 0;
                }
            }
            // 如果播放通道数大于2，其余通道填充0
            for (int j = 2; j < playback_channels; j++) {
                playback_buffer[i * playback_channels + j] = 0;
            }
        }
        
        // 检查播放设备状态
        snd_pcm_state_t state = snd_pcm_state(playback_handle);
        if (state == SND_PCM_STATE_XRUN) {
            underrun_count++;
            if (underrun_count > 10) {
                fprintf(stderr, "播放设备频繁下溢，可能存在系统问题\n");
                break;
            }
            if (recover_playback() < 0) {
                break;
            }
            continue;
        }
        
        // 向播放设备写入数据 - 根据模式选择写入方式
        if (use_mmap) {
            err = mmap_write_audio(playback_handle, playback_buffer, frames_to_process, playback_channels);
        } else {
            err = snd_pcm_writei(playback_handle, playback_buffer, frames_to_process);
        }

        if (err == -EAGAIN) {
            usleep(100); // 短暂等待
            continue;
        } else if (err < 0) {
            if (err == -EPIPE) {
                underrun_count++;
                if (underrun_count > 10) {
                    fprintf(stderr, "播放设备频繁下溢，可能存在系统问题\n");
                    break;
                }
                if (recover_playback() < 0) {
                    break;
                }
            } else {
                fprintf(stderr, "写入播放数据错误: %s\n", snd_strerror(err));
                break;
            }
            continue;
        }
        
        // 重置下溢计数器
        if (underrun_count > 0) {
            underrun_count = 0;
        }
    }
    
    free(capture_buffer);
    free(playback_buffer);
}

// 清理资源
void cleanup() {
    if (capture_handle) {
        snd_pcm_close(capture_handle);
        capture_handle = NULL;
    }
    if (playback_handle) {
        snd_pcm_close(playback_handle);
        playback_handle = NULL;
    }
    printf("资源清理完成\n");
}

// 显示帮助信息
void show_usage(const char *prog_name) {
    printf("用法: %s [选项]\n", prog_name);
    printf("选项:\n");
    printf("  -i, --input <dev>    指定采集设备 (默认: hw:0,0)\n");
    printf("  -o, --output <dev>   指定播放设备 (默认: hw:2,0)\n");
    printf("  -I, --input-ch <n>   指定采集通道数 (默认: %d)\n", DEFAULT_CAPTURE_CHANNELS);
    printf("  -O, --output-ch <n>  指定播放通道数 (默认: %d)\n", DEFAULT_PLAYBACK_CHANNELS);
    printf("  -c, --ch1 <n>        选择第一个通道 (默认: 0)\n");
    printf("  -C, --ch2 <n>        选择第二个通道 (默认: 1)\n");
    printf("  -f, --frame <ms>     设置帧长 (1,4,8ms, 默认: 4)\n");
    printf("  -m, --mmap           使用MMAP模式 (零拷贝，更低延迟)\n");
    printf("  -d, --debug          启用调试模式\n");
    printf("  -h, --help           显示此帮助信息\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s                           使用默认设置\n", prog_name);
    printf("  %s -i hw:1,0 -o hw:3,0       指定采集和播放设备\n", prog_name);
    printf("  %s -I 8 -O 4 -c 2 -C 3       8通道采集，4通道播放，选择通道2,3\n", prog_name);
    printf("  %s -c 2 -C 3 -f 1 -l -m      选择通道2,3，1ms帧长，低延迟+MMAP模式\n", prog_name);
    printf("  %s -m -f 4                   使用MMAP模式，4ms帧长\n", prog_name);
    printf("  %s -d                        启用调试模式\n", prog_name);
}

int main(int argc, char *argv[]) {
    int opt;
    struct option long_options[] = {
        {"input", required_argument, 0, 'i'},
        {"output", required_argument, 0, 'o'},
        {"input-ch", required_argument, 0, 'I'},
        {"output-ch", required_argument, 0, 'O'},
        {"ch1", required_argument, 0, 'c'},
        {"ch2", required_argument, 0, 'C'},
        {"frame", required_argument, 0, 'f'},
        {"mmap", no_argument, 0, 'm'},
        {"debug", no_argument, 0, 'd'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };

    // 解析命令行参数
    while ((opt = getopt_long(argc, argv, "i:o:I:O:c:C:f:lmdh", long_options, NULL)) != -1) {
        switch (opt) {
            case 'i':
                strncpy(capture_device, optarg, sizeof(capture_device) - 1);
                capture_device[sizeof(capture_device) - 1] = '\0';
                break;
            case 'o':
                strncpy(playback_device, optarg, sizeof(playback_device) - 1);
                playback_device[sizeof(playback_device) - 1] = '\0';
                break;
            case 'I':
                capture_channels = atoi(optarg);
                if (capture_channels <= 0 || capture_channels > 64) {
                    fprintf(stderr, "错误: 采集通道数必须在1-64之间\n");
                    return 1;
                }
                break;
            case 'O':
                playback_channels = atoi(optarg);
                if (playback_channels <= 0 || playback_channels > 32) {
                    fprintf(stderr, "错误: 播放通道数必须在1-32之间\n");
                    return 1;
                }
                break;
            case 'c':
                ch1 = atoi(optarg);
                break;
            case 'C':
                ch2 = atoi(optarg);
                break;
            case 'f':
                frame_time = atoi(optarg);
                if (frame_time != 1 && frame_time != 4 && frame_time != 8) {
                    fprintf(stderr, "错误: 帧长必须是 1, 4, 或 8ms\n");
                    return 1;
                }
                break;
            case 'm':
                use_mmap = 1;
                printf("MMAP模式已启用\n");
                break;
            case 'd':
                debug_mode = 1;
                printf("调试模式已启用\n");
                break;
            case 'h':
                show_usage(argv[0]);
                return 0;
            default:
                show_usage(argv[0]);
                return 1;
        }
    }

    // 验证通道参数
    if (ch1 < 0 || ch1 >= capture_channels) {
        fprintf(stderr, "错误: 通道1超出范围 (0-%d)\n", capture_channels-1);
        return 1;
    }
    if (ch2 < 0 || ch2 >= capture_channels) {
        fprintf(stderr, "错误: 通道2超出范围 (0-%d)\n", capture_channels-1);
        return 1;
    }
    
    printf("=== 嵌入式Linux音频测试程序 ===\n");
    printf("配置: 采集通道%d,%d -> 播放, 帧长%dms%s\n",
           ch1, ch2, frame_time,
           use_mmap ? " (MMAP模式)" : "");
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 设置高优先级（需要root权限）
    if (set_high_priority() != 0) {
        printf("警告: 无法设置高优先级，继续运行...\n");
    }
    
    // 初始化ALSA设备
    if (init_capture_device() != 0) {
        cleanup();
        return 1;
    }
    
    if (init_playback_device() != 0) {
        cleanup();
        return 1;
    }
    
    // 开始音频处理
    audio_loop();
    
    // 清理资源
    cleanup();
    
    printf("程序正常退出\n");
    return 0;
}