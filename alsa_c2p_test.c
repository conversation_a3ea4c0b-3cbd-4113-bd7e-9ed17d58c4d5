#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <getopt.h>
#include <pthread.h>
#include <sched.h>
#include <alsa/asoundlib.h>

// 音频参数定义
#define SAMPLE_RATE     48000
#define CAPTURE_CHANNELS 32
#define PLAYBACK_CHANNELS 2
#define FORMAT          SND_PCM_FORMAT_S32_LE
#define SAMPLE_SIZE     4  // S32_LE每样本4字节

// 全局变量
static int running = 1;
static snd_pcm_t *capture_handle = NULL;
static snd_pcm_t *playback_handle = NULL;
static int ch1 = 0, ch2 = 1;  // 默认选择通道0和1
static int frame_time = 4;    // 默认4ms帧长
static int debug_mode = 0;    // 调试模式

// 信号处理函数
void signal_handler(int sig) {
    printf("\n接收到信号 %d, 正在退出...\n", sig);
    running = 0;
}

// 设置进程优先级
int set_high_priority() {
    struct sched_param param;
    param.sched_priority = 90;  // 设置为高优先级
    
    if (sched_setscheduler(0, SCHED_FIFO, &param) != 0) {
        perror("设置实时优先级失败");
        return -1;
    }
    
    printf("进程优先级已设置为实时模式，优先级: %d\n", param.sched_priority);
    return 0;
}

// 初始化ALSA采集设备
int init_capture_device() {
    int err;
    snd_pcm_hw_params_t *hw_params;
    
    // 打开PCM设备
    if ((err = snd_pcm_open(&capture_handle, "hw:0,0", SND_PCM_STREAM_CAPTURE, 0)) < 0) {
        fprintf(stderr, "无法打开采集设备 hw:0,0: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 分配硬件参数结构体
    if ((err = snd_pcm_hw_params_malloc(&hw_params)) < 0) {
        fprintf(stderr, "无法分配硬件参数结构体: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 初始化硬件参数
    if ((err = snd_pcm_hw_params_any(capture_handle, hw_params)) < 0) {
        fprintf(stderr, "无法初始化硬件参数: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置访问模式
    if ((err = snd_pcm_hw_params_set_access(capture_handle, hw_params, SND_PCM_ACCESS_RW_INTERLEAVED)) < 0) {
        fprintf(stderr, "无法设置访问模式: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置采样格式
    if ((err = snd_pcm_hw_params_set_format(capture_handle, hw_params, FORMAT)) < 0) {
        fprintf(stderr, "无法设置采样格式: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置采样率
    unsigned int rate = SAMPLE_RATE;
    if ((err = snd_pcm_hw_params_set_rate_near(capture_handle, hw_params, &rate, 0)) < 0) {
        fprintf(stderr, "无法设置采样率: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置通道数
    if ((err = snd_pcm_hw_params_set_channels(capture_handle, hw_params, CAPTURE_CHANNELS)) < 0) {
        fprintf(stderr, "无法设置通道数: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置周期大小（帧数）
    snd_pcm_uframes_t frames = (SAMPLE_RATE * frame_time) / 1000;
    if ((err = snd_pcm_hw_params_set_period_size_near(capture_handle, hw_params, &frames, 0)) < 0) {
        fprintf(stderr, "无法设置周期大小: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置缓冲区大小为4个周期
    snd_pcm_uframes_t buffer_size = frames * 4;
    if ((err = snd_pcm_hw_params_set_buffer_size_near(capture_handle, hw_params, &buffer_size)) < 0) {
        fprintf(stderr, "无法设置缓冲区大小: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 应用硬件参数
    if ((err = snd_pcm_hw_params(capture_handle, hw_params)) < 0) {
        fprintf(stderr, "无法应用硬件参数: %s\n", snd_strerror(err));
        return -1;
    }
    
    snd_pcm_hw_params_free(hw_params);
    
    // 准备PCM设备
    if ((err = snd_pcm_prepare(capture_handle)) < 0) {
        fprintf(stderr, "无法准备PCM设备: %s\n", snd_strerror(err));
        return -1;
    }
    
    printf("采集设备初始化成功: hw:0,0, %d通道, %dHz, %dms帧长\n", 
           CAPTURE_CHANNELS, SAMPLE_RATE, frame_time);
    
    return 0;
}

// 初始化ALSA播放设备
int init_playback_device() {
    int err;
    snd_pcm_hw_params_t *hw_params;
    
    // 打开PCM设备
    if ((err = snd_pcm_open(&playback_handle, "hw:2,0", SND_PCM_STREAM_PLAYBACK, 0)) < 0) {
        fprintf(stderr, "无法打开播放设备 hw:2,0: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 分配硬件参数结构体
    if ((err = snd_pcm_hw_params_malloc(&hw_params)) < 0) {
        fprintf(stderr, "无法分配硬件参数结构体: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 初始化硬件参数
    if ((err = snd_pcm_hw_params_any(playback_handle, hw_params)) < 0) {
        fprintf(stderr, "无法初始化硬件参数: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置访问模式
    if ((err = snd_pcm_hw_params_set_access(playback_handle, hw_params, SND_PCM_ACCESS_RW_INTERLEAVED)) < 0) {
        fprintf(stderr, "无法设置访问模式: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置采样格式
    if ((err = snd_pcm_hw_params_set_format(playback_handle, hw_params, FORMAT)) < 0) {
        fprintf(stderr, "无法设置采样格式: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置采样率
    unsigned int rate = SAMPLE_RATE;
    if ((err = snd_pcm_hw_params_set_rate_near(playback_handle, hw_params, &rate, 0)) < 0) {
        fprintf(stderr, "无法设置采样率: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置通道数
    if ((err = snd_pcm_hw_params_set_channels(playback_handle, hw_params, PLAYBACK_CHANNELS)) < 0) {
        fprintf(stderr, "无法设置通道数: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置周期大小（帧数）
    snd_pcm_uframes_t frames = (SAMPLE_RATE * frame_time) / 1000;
    if ((err = snd_pcm_hw_params_set_period_size_near(playback_handle, hw_params, &frames, 0)) < 0) {
        fprintf(stderr, "无法设置周期大小: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 设置缓冲区大小为8个周期（增加缓冲区深度）
    snd_pcm_uframes_t buffer_size = frames * 4;
    if ((err = snd_pcm_hw_params_set_buffer_size_near(playback_handle, hw_params, &buffer_size)) < 0) {
        fprintf(stderr, "无法设置缓冲区大小: %s\n", snd_strerror(err));
        return -1;
    }
    
    // 应用硬件参数
    if ((err = snd_pcm_hw_params(playback_handle, hw_params)) < 0) {
        fprintf(stderr, "无法应用硬件参数: %s\n", snd_strerror(err));
        return -1;
    }
    
    snd_pcm_hw_params_free(hw_params);
    
    // 获取实际的参数值并显示
    snd_pcm_uframes_t actual_buffer_size, actual_period_size;
    snd_pcm_hw_params_get_buffer_size(hw_params, &actual_buffer_size);
    snd_pcm_hw_params_get_period_size(hw_params, &actual_period_size, 0);
    
    if (debug_mode) {
        printf("播放设备实际参数: buffer_size=%lu, period_size=%lu\n", 
               actual_buffer_size, actual_period_size);
    }
    
    // 准备PCM设备
    if ((err = snd_pcm_prepare(playback_handle)) < 0) {
        fprintf(stderr, "无法准备PCM设备: %s\n", snd_strerror(err));
        return -1;
    }
    
    printf("播放设备初始化成功: hw:2,0, %d通道, %dHz\n", 
           PLAYBACK_CHANNELS, SAMPLE_RATE);
    
    return 0;
}

// 恢复播放设备
int recover_playback() {
    int err;
    printf("播放设备缓冲区下溢，恢复中...\n");
    
    if ((err = snd_pcm_prepare(playback_handle)) < 0) {
        fprintf(stderr, "无法准备播放设备: %s\n", snd_strerror(err));
        return err;
    }
    
    // 预填充一些静音数据到播放缓冲区
    snd_pcm_uframes_t frames_to_process = (SAMPLE_RATE * frame_time) / 1000;
    int32_t *silence_buffer = calloc(frames_to_process * PLAYBACK_CHANNELS, SAMPLE_SIZE);
    if (silence_buffer) {
        // 写入2个周期的静音数据
        for (int i = 0; i < 2; i++) {
            snd_pcm_writei(playback_handle, silence_buffer, frames_to_process);
        }
        free(silence_buffer);
    }
    
    return 0;
}

// 音频处理主循环
void audio_loop() {
    int err;
    snd_pcm_uframes_t frames_to_process = (SAMPLE_RATE * frame_time) / 1000;
    static int underrun_count = 0;
    
    // 分配缓冲区
    int32_t *capture_buffer = malloc(frames_to_process * CAPTURE_CHANNELS * SAMPLE_SIZE);
    int32_t *playback_buffer = malloc(frames_to_process * PLAYBACK_CHANNELS * SAMPLE_SIZE);
    
    if (!capture_buffer || !playback_buffer) {
        fprintf(stderr, "无法分配音频缓冲区\n");
        return;
    }
    
    // 预填充播放缓冲区，避免开始时的下溢
    memset(playback_buffer, 0, frames_to_process * PLAYBACK_CHANNELS * SAMPLE_SIZE);
    for (int i = 0; i < 3; i++) {
        snd_pcm_writei(playback_handle, playback_buffer, frames_to_process);
    }
    
    printf("开始音频处理循环，选择通道: %d, %d\n", ch1, ch2);
    printf("按 Ctrl+C 停止程序\n");
    
    while (running) {
        // 从采集设备读取数据
        err = snd_pcm_readi(capture_handle, capture_buffer, frames_to_process);
        if (err == -EAGAIN) {
            usleep(100); // 短暂等待
            continue;
        } else if (err < 0) {
            if (err == -EPIPE) {
                printf("采集设备缓冲区溢出，恢复中...\n");
                snd_pcm_prepare(capture_handle);
            } else {
                fprintf(stderr, "读取采集数据错误: %s\n", snd_strerror(err));
                break;
            }
            continue;
        }
        
        // 检查是否读取到预期的帧数
        if (err != (int)frames_to_process) {
            printf("警告: 只读取到 %d 帧，预期 %lu 帧\n", err, frames_to_process);
            continue;
        }
        
        // 提取选定的2个通道数据
        for (snd_pcm_uframes_t i = 0; i < frames_to_process; i++) {
            // 左声道
            playback_buffer[i * PLAYBACK_CHANNELS] = 
                capture_buffer[i * CAPTURE_CHANNELS + ch1];
            // 右声道
            playback_buffer[i * PLAYBACK_CHANNELS + 1] = 
                capture_buffer[i * CAPTURE_CHANNELS + ch2];
        }
        
        // 检查播放设备状态
        snd_pcm_state_t state = snd_pcm_state(playback_handle);
        if (state == SND_PCM_STATE_XRUN) {
            underrun_count++;
            if (underrun_count > 10) {
                fprintf(stderr, "播放设备频繁下溢，可能存在系统问题\n");
                break;
            }
            if (recover_playback() < 0) {
                break;
            }
            continue;
        }
        
        // 向播放设备写入数据
        err = snd_pcm_writei(playback_handle, playback_buffer, frames_to_process);
        if (err == -EAGAIN) {
            usleep(100); // 短暂等待
            continue;
        } else if (err < 0) {
            if (err == -EPIPE) {
                underrun_count++;
                if (underrun_count > 10) {
                    fprintf(stderr, "播放设备频繁下溢，可能存在系统问题\n");
                    break;
                }
                if (recover_playback() < 0) {
                    break;
                }
            } else {
                fprintf(stderr, "写入播放数据错误: %s\n", snd_strerror(err));
                break;
            }
            continue;
        }
        
        // 重置下溢计数器
        if (underrun_count > 0) {
            underrun_count = 0;
        }
    }
    
    free(capture_buffer);
    free(playback_buffer);
}

// 清理资源
void cleanup() {
    if (capture_handle) {
        snd_pcm_close(capture_handle);
        capture_handle = NULL;
    }
    if (playback_handle) {
        snd_pcm_close(playback_handle);
        playback_handle = NULL;
    }
    printf("资源清理完成\n");
}

// 显示帮助信息
void show_usage(const char *prog_name) {
    printf("用法: %s [选项]\n", prog_name);
    printf("选项:\n");
    printf("  -c, --ch1 <n>        选择第一个通道 (0-%d, 默认: 0)\n", CAPTURE_CHANNELS-1);
    printf("  -C, --ch2 <n>        选择第二个通道 (0-%d, 默认: 1)\n", CAPTURE_CHANNELS-1);
    printf("  -f, --frame <ms>     设置帧长 (1,4,8ms, 默认: 4)\n");
    printf("  -d, --debug          启用调试模式\n");
    printf("  -h, --help           显示此帮助信息\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s                   使用默认设置\n", prog_name);
    printf("  %s -c 2 -C 3 -f 8    选择通道2,3，8ms帧长\n", prog_name);
    printf("  %s -d                启用调试模式\n", prog_name);
}

int main(int argc, char *argv[]) {
    int opt;
    struct option long_options[] = {
        {"ch1", required_argument, 0, 'c'},
        {"ch2", required_argument, 0, 'C'},
        {"frame", required_argument, 0, 'f'},
        {"debug", no_argument, 0, 'd'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    
    // 解析命令行参数
    while ((opt = getopt_long(argc, argv, "c:C:f:dh", long_options, NULL)) != -1) {
        switch (opt) {
            case 'c':
                ch1 = atoi(optarg);
                if (ch1 < 0 || ch1 >= CAPTURE_CHANNELS) {
                    fprintf(stderr, "错误: 通道1超出范围 (0-%d)\n", CAPTURE_CHANNELS-1);
                    return 1;
                }
                break;
            case 'C':
                ch2 = atoi(optarg);
                if (ch2 < 0 || ch2 >= CAPTURE_CHANNELS) {
                    fprintf(stderr, "错误: 通道2超出范围 (0-%d)\n", CAPTURE_CHANNELS-1);
                    return 1;
                }
                break;
            case 'f':
                frame_time = atoi(optarg);
                if (frame_time != 1 && frame_time != 4 && frame_time != 8) {
                    fprintf(stderr, "错误: 帧长必须是 1, 4, 或 8ms\n");
                    return 1;
                }
                break;
            case 'd':
                debug_mode = 1;
                printf("调试模式已启用\n");
                break;
            case 'h':
                show_usage(argv[0]);
                return 0;
            default:
                show_usage(argv[0]);
                return 1;
        }
    }
    
    printf("=== 嵌入式Linux音频测试程序 ===\n");
    printf("配置: 采集通道%d,%d -> 播放, 帧长%dms\n", ch1, ch2, frame_time);
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 设置高优先级（需要root权限）
    if (set_high_priority() != 0) {
        printf("警告: 无法设置高优先级，继续运行...\n");
    }
    
    // 初始化ALSA设备
    if (init_capture_device() != 0) {
        cleanup();
        return 1;
    }
    
    if (init_playback_device() != 0) {
        cleanup();
        return 1;
    }
    
    // 开始音频处理
    audio_loop();
    
    // 清理资源
    cleanup();
    
    printf("程序正常退出\n");
    return 0;
}