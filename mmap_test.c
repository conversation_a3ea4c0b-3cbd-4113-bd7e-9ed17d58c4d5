#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <alsa/asoundlib.h>

static int running = 1;

void signal_handler(int sig) {
    printf("\n接收到信号 %d, 正在退出...\n", sig);
    running = 0;
}

// 简单的MMAP读取测试
int test_mmap_read(const char *device) {
    snd_pcm_t *handle;
    snd_pcm_hw_params_t *hw_params;
    int err;
    
    printf("测试MMAP读取: %s\n", device);
    
    // 打开设备
    if ((err = snd_pcm_open(&handle, device, SND_PCM_STREAM_CAPTURE, 0)) < 0) {
        printf("无法打开设备 %s: %s\n", device, snd_strerror(err));
        return -1;
    }
    
    // 设置硬件参数
    snd_pcm_hw_params_malloc(&hw_params);
    snd_pcm_hw_params_any(handle, hw_params);
    
    // 尝试设置MMAP访问模式
    if ((err = snd_pcm_hw_params_set_access(handle, hw_params, SND_PCM_ACCESS_MMAP_INTERLEAVED)) < 0) {
        printf("设备 %s 不支持MMAP模式: %s\n", device, snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(handle);
        return -1;
    }
    
    // 设置基本参数
    snd_pcm_hw_params_set_format(handle, hw_params, SND_PCM_FORMAT_S32_LE);
    unsigned int rate = 48000;
    snd_pcm_hw_params_set_rate_near(handle, hw_params, &rate, 0);
    snd_pcm_hw_params_set_channels(handle, hw_params, 2);
    
    snd_pcm_uframes_t frames = 192; // 4ms @ 48kHz
    snd_pcm_hw_params_set_period_size_near(handle, hw_params, &frames, 0);
    
    snd_pcm_uframes_t buffer_size = frames * 4;
    snd_pcm_hw_params_set_buffer_size_near(handle, hw_params, &buffer_size);
    
    if ((err = snd_pcm_hw_params(handle, hw_params)) < 0) {
        printf("无法设置硬件参数: %s\n", snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(handle);
        return -1;
    }
    
    snd_pcm_hw_params_free(hw_params);
    
    if ((err = snd_pcm_prepare(handle)) < 0) {
        printf("无法准备设备: %s\n", snd_strerror(err));
        snd_pcm_close(handle);
        return -1;
    }
    
    printf("设备 %s 支持MMAP模式！\n", device);
    
    // 简单的MMAP读取测试
    const snd_pcm_channel_area_t *areas;
    snd_pcm_uframes_t offset, frames_available;
    int test_count = 0;
    
    snd_pcm_start(handle);
    
    while (running && test_count < 10) {
        frames_available = frames;
        
        err = snd_pcm_mmap_begin(handle, &areas, &offset, &frames_available);
        if (err < 0) {
            if (err == -EPIPE) {
                printf("缓冲区溢出，恢复中...\n");
                snd_pcm_prepare(handle);
                snd_pcm_start(handle);
                continue;
            }
            printf("mmap_begin错误: %s\n", snd_strerror(err));
            break;
        }
        
        printf("MMAP读取成功: offset=%lu, frames=%lu\n", offset, frames_available);
        
        err = snd_pcm_mmap_commit(handle, offset, frames_available);
        if (err < 0) {
            printf("mmap_commit错误: %s\n", snd_strerror(err));
            break;
        }
        
        test_count++;
        usleep(10000); // 10ms
    }
    
    snd_pcm_close(handle);
    return 0;
}

// 简单的MMAP写入测试
int test_mmap_write(const char *device) {
    snd_pcm_t *handle;
    snd_pcm_hw_params_t *hw_params;
    int err;
    
    printf("测试MMAP写入: %s\n", device);
    
    // 打开设备
    if ((err = snd_pcm_open(&handle, device, SND_PCM_STREAM_PLAYBACK, 0)) < 0) {
        printf("无法打开设备 %s: %s\n", device, snd_strerror(err));
        return -1;
    }
    
    // 设置硬件参数
    snd_pcm_hw_params_malloc(&hw_params);
    snd_pcm_hw_params_any(handle, hw_params);
    
    // 尝试设置MMAP访问模式
    if ((err = snd_pcm_hw_params_set_access(handle, hw_params, SND_PCM_ACCESS_MMAP_INTERLEAVED)) < 0) {
        printf("设备 %s 不支持MMAP模式: %s\n", device, snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(handle);
        return -1;
    }
    
    // 设置基本参数
    snd_pcm_hw_params_set_format(handle, hw_params, SND_PCM_FORMAT_S32_LE);
    unsigned int rate = 48000;
    snd_pcm_hw_params_set_rate_near(handle, hw_params, &rate, 0);
    snd_pcm_hw_params_set_channels(handle, hw_params, 2);
    
    snd_pcm_uframes_t frames = 192; // 4ms @ 48kHz
    snd_pcm_hw_params_set_period_size_near(handle, hw_params, &frames, 0);
    
    snd_pcm_uframes_t buffer_size = frames * 4;
    snd_pcm_hw_params_set_buffer_size_near(handle, hw_params, &buffer_size);
    
    if ((err = snd_pcm_hw_params(handle, hw_params)) < 0) {
        printf("无法设置硬件参数: %s\n", snd_strerror(err));
        snd_pcm_hw_params_free(hw_params);
        snd_pcm_close(handle);
        return -1;
    }
    
    snd_pcm_hw_params_free(hw_params);
    
    if ((err = snd_pcm_prepare(handle)) < 0) {
        printf("无法准备设备: %s\n", snd_strerror(err));
        snd_pcm_close(handle);
        return -1;
    }
    
    printf("设备 %s 支持MMAP模式！\n", device);
    
    // 简单的MMAP写入测试
    const snd_pcm_channel_area_t *areas;
    snd_pcm_uframes_t offset, frames_available;
    int test_count = 0;
    
    while (running && test_count < 10) {
        frames_available = frames;
        
        err = snd_pcm_mmap_begin(handle, &areas, &offset, &frames_available);
        if (err < 0) {
            if (err == -EPIPE) {
                printf("缓冲区下溢，恢复中...\n");
                snd_pcm_prepare(handle);
                continue;
            }
            printf("mmap_begin错误: %s\n", snd_strerror(err));
            break;
        }
        
        // 写入静音数据
        int32_t *data = (int32_t *)((char *)areas[0].addr + (areas[0].first + offset * areas[0].step) / 8);
        memset(data, 0, frames_available * 2 * sizeof(int32_t));
        
        printf("MMAP写入成功: offset=%lu, frames=%lu\n", offset, frames_available);
        
        err = snd_pcm_mmap_commit(handle, offset, frames_available);
        if (err < 0) {
            printf("mmap_commit错误: %s\n", snd_strerror(err));
            break;
        }
        
        test_count++;
        usleep(10000); // 10ms
    }
    
    snd_pcm_close(handle);
    return 0;
}

int main(int argc, char *argv[]) {
    signal(SIGINT, signal_handler);
    
    printf("=== ALSA MMAP支持测试 ===\n");
    
    if (argc < 2) {
        printf("用法: %s <capture_device> [playback_device]\n", argv[0]);
        printf("示例: %s hw:0,0 hw:1,0\n", argv[0]);
        return 1;
    }
    
    // 测试采集设备
    if (test_mmap_read(argv[1]) == 0) {
        printf("✓ 采集设备 %s 支持MMAP\n", argv[1]);
    } else {
        printf("✗ 采集设备 %s 不支持MMAP\n", argv[1]);
    }
    
    // 测试播放设备（如果提供）
    if (argc > 2) {
        if (test_mmap_write(argv[2]) == 0) {
            printf("✓ 播放设备 %s 支持MMAP\n", argv[2]);
        } else {
            printf("✗ 播放设备 %s 不支持MMAP\n", argv[2]);
        }
    }
    
    printf("测试完成\n");
    return 0;
}
